package sk.isdd.dr.health;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class HealthCheckService {

    private final DataSource dataSource;

    @Value("${keycloak.url}")
    private String keycloakUrl;

    @Value("${shl.url}")
    private String shlUrl;

    @Value("${mailchimp.health-url}")
    private String mailchimpHealthUrl;

    @Value("${mailchimp.api-key}")
    private String mailchimpApiKey;

    public HealthCheckService(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    public boolean isAppUp() {
        // if service is running, app is up
        return true;
    }

    public boolean isDbUp() {
        try (Connection conn = dataSource.getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }

    public boolean isUrlUp(String rawUrl) {
        return isUrlUp(rawUrl, "GET", null);
    }

    public boolean isUrlUp(String rawUrl, String method, String requestBody) {
        if (rawUrl == null || rawUrl.isBlank()) {
            return false;
        }
        try {
            java.net.URI uri = java.net.URI.create(rawUrl);
            URL url = uri.toURL();
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(2000);
            conn.setReadTimeout(3000);
            conn.setRequestMethod(method == null ? "GET" : method);
            conn.setInstanceFollowRedirects(true);
            if (requestBody != null && !requestBody.isBlank()) {
                conn.setDoOutput(true);
                conn.setRequestProperty("Content-Type", "application/json; charset=utf-8");
                byte[] payload = requestBody.getBytes(StandardCharsets.UTF_8);
                conn.setFixedLengthStreamingMode(payload.length);
                try (OutputStream os = conn.getOutputStream()) {
                    os.write(payload);
                    os.flush();
                }
            }
            int code = conn.getResponseCode();
            return code >= 200 && code < 400;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isKeycloakUp() {
        return isUrlUp(keycloakUrl);
    }

    public boolean isShlUp() {
        return isUrlUp(shlUrl);
    }

    public boolean isMailchimpUp() {
        // Mandrill (Mailchimp) users/info.json expects POST with {"key":"API_KEY"}
        if (mailchimpHealthUrl == null || mailchimpHealthUrl.isBlank() || mailchimpApiKey == null
                || mailchimpApiKey.isBlank()) {
            return false;
        }
        String body = String.format("{\"key\":\"%s\"}", mailchimpApiKey);
        return isUrlUp(mailchimpHealthUrl, "POST", body);
    }
}
