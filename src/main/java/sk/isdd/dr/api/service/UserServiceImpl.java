package sk.isdd.dr.api.service;

import java.util.List;

import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.Consents;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.User;
import sk.isdd.dr.api.dto.request.UpdateUserPasswordRequest;
import sk.isdd.dr.api.dto.request.UpdateUserDataRequest;
import sk.isdd.dr.api.exception.business.BadRequestException;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.mapper.MainMapper;
import sk.isdd.dr.api.service.api.AttachmentService;
import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.api.service.api.UserService;
import sk.isdd.dr.auth.UserManager;
import sk.isdd.dr.auth.UserRole;
import sk.isdd.dr.common.enums.AttachmentType;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.jpa.entity.SubjectEntity;

@Service
public class UserServiceImpl extends BaseService implements UserService {

    @Autowired
    private LogService auditLogger;

    @Autowired
    private UserManager userManager;

    @Autowired
    private SubjectService subjectService;

    @Autowired
    private AttachmentService attachmentService;

    private final MainMapper mapper;

    public UserServiceImpl(MainMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<User> getCoaches() {
        return userManager.getUsersByRole(UserRole.COACH, null, null)
                .stream()
                .map((user) -> mapper.mapUserRepresentationToUser(user)).toList();
    }

    @Override
    @Transactional
    public User updateUser(UpdateUserDataRequest request, MultipartFile cvFile) {
        if (!request.getConsents().isTermsOfProject() && getCurrentUser().isClientPreRegistered()) {
            // nesuhlas s podmienkami projektu
            throw new BadRequestException();
        }

        UserRepresentation user = userManager.getUserById(getCurrentUser().getId())
                .orElseThrow(() -> new NotFoundException());

        // set additional consents to actual consents
        SubjectEntity subjectEntity = subjectService.loadSubject(getCurrentUser().getSubjectId());
        Consents actualConsents = mapper.intConsentsToDTO(subjectEntity.getConsents());
        if (getCurrentUser().isClientPreRegistered()) {
            actualConsents.setTermsOfProject(request.getConsents().isTermsOfProject());
            actualConsents.setTestReportsVisibility(request.getConsents().isTestReportsVisibility());
        }

        Subject subject = new Subject();

        subject.setId(getCurrentUser().getSubjectId());
        subject.setName(request.getName());
        subject.setSurname(request.getSurname());
        subject.setIdNumber(request.getIdNumber());
        subject.setConsents(actualConsents);
        subject.setPhoneNumber(request.getPhoneNumber());
        subject.setLinkedInProfile(request.getLinkedInProfile());

        Subject updatedSubject = subjectService.updateSubject(subject);

        userManager.finishRegistration(updatedSubject);

        if (cvFile != null) {
            attachmentService.uploadAttachment(subjectService.loadSubject(getCurrentUser().getSubjectId()),
                    AttachmentType.CV, cvFile);
        }

        auditLogger.log(getCurrentUser().getId(), subjectService.loadSubject(getCurrentUser().getSubjectId()),
                LogAction.USER_UPDATED);

        return mapper.mapUserRepresentationToUser(user);
    }

    @Override
    public User updateUserPassword(UpdateUserPasswordRequest request) {
        UserRepresentation user = userManager.changeClientPassword(getCurrentUser().getId(), request.getPassword(),
                request.getPassword());
        auditLogger.log(getCurrentUser().getId(), subjectService.loadSubject(getCurrentUser().getSubjectId()), LogAction.USER_PASSWORD_CHANGE);
        return mapper.mapUserRepresentationToUser(user);
    }

}
