package sk.isdd.dr.email;

import java.util.Map;

import lombok.Data;

@Data
public class EmailTemplateVariables {

    private final String name;

    private final String surname;

    private final String text;

    private final String email;

    public Map<String, String> toMap() {
        return Map.of(
            "name", name == null ? "" : name,
            "surname", surname == null ? "" : surname,
            "text", text == null ? "" : text,
            "email", email == null ? "" : email
        );
    }

}
