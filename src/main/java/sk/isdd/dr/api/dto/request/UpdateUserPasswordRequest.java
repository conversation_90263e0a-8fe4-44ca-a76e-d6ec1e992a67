package sk.isdd.dr.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class UpdateUserPasswordRequest {

    @NotBlank
    @JsonProperty(value = "password", required = true)
    private String password;

    @NotBlank
    @JsonProperty(value = "password_verify", required = true)
    private String passwordVerify;

}
