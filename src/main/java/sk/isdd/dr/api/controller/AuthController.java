package sk.isdd.dr.api.controller;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import sk.isdd.dr.api.dto.User;
import sk.isdd.dr.api.dto.request.RegisterRequest;
import sk.isdd.dr.api.dto.request.RegisterWithEmployerRequest;
import sk.isdd.dr.api.service.api.AuthService;

@RestController
@RequestMapping("/auth")
@Tag(name = "Auth Controller")
public class AuthController {

    private AuthService authService;

    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping(path = "/register", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @SecurityRequirements
    public ResponseEntity<User> register(@Valid @RequestBody RegisterRequest request, @RequestParam(required = false) Boolean retry) {
        return ResponseEntity.ok(authService.register(request, retry));
    }

    @PostMapping(path = "/register/with-employer", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @SecurityRequirements
    public ResponseEntity<User> registerWithEmployer(@Valid @RequestBody RegisterWithEmployerRequest request, @RequestParam(required = false) Boolean retry) {
        return ResponseEntity.ok(authService.registerWithEmployer(request, retry));
    }

}
