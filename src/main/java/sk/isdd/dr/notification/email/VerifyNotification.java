package sk.isdd.dr.notification.email;

import java.util.List;

import org.springframework.stereotype.Component;

import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.repository.SubjectRepository;
import sk.isdd.dr.notification.email.api.EmailNotification;

@Component
public class VerifyNotification extends EmailNotification {

    private final SubjectRepository subjectRepository;

    public VerifyNotification(SubjectRepository subjectRepository) {
        this.subjectRepository = subjectRepository;
    }

    @Override
    protected List<SubjectEntity> getSubjects() {
        return subjectRepository.getNotVerifiedSubjects().stream()
                .toList();
    }

    @Override
    protected EmailTemplateCode getTemplateCode() {
        return EmailTemplateCode.REGISTER_ZOZ_REMINDER;
    }

    @Override
    public boolean isScheduled() {
        return false;
    }

    @Override
    public LogAction getAuditLogCode() {
        return LogAction.SEND_NOTIFICATION_VERIFY;
    }

}
