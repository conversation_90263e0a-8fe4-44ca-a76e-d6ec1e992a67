server:
  port: 8080
  servlet:
    context-path: /api

spring:
  # main:
  #   allow-circular-references: true
  application:
    name: dr-api
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_URL}/realms/${KEYCLOAK_REALM}
  datasource:
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.mariadb.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: none # validate
    show-sql: false
    properties:
      hibernate:
        format_sql: true
  flyway:
    enabled: true
    baseline-on-migrate: true
    baseline-version: 1
    validate-on-migrate: true
  # mail:
  #   host: ${MAIL_URL}
  #   port: ${MAIL_PORT}
  #   username: ${MAIL_USERNAME}
  #   password: ${MAIL_PASSWORD}
  #   properties:
  #     mail:
  #       smtp:
  #         auth: true
  #         starttls:
  #           enabled: true
  #           required: true
  #   default-encoding: UTF-8

attachment:
  root-path: files/attachments

frontend-url: ${FRONTEND_URL}

shl:
  url: ${SHL_URL}
  process-candidate-registration:
    username: ${SHL_USERNAME}
    password: ${SHL_PASSWORD}
    party-id: ${SHL_PARTY_ID}
    integration-config-id: ${SHL_INTEGRATION_CONFIG_ID}
    partner-name: ${SHL_PARTNER_NAME}
    pushback-host: ${SHL_PUSHBACK_HOST}
    deadline-days: 14

keycloak:
  url: ${KEYCLOAK_URL}
  realm: ${KEYCLOAK_REALM}
  client-id: dr-manager
  client-secret: ${KEYCLOAK_CLIENT_SECRET}
  auth-client-id: dr-auth
  reset-password-link-lifespan: 24 # in hours

springdoc:
  swagger-ui:
    path: /swagger-ui.html

chatbot:
  chatgpt:
    url: https://api.openai.com/v1/chat/completions
    model-key: gpt-4o
    model-name: GPT-4o
    key: ${CHATBOT_CHATGPT_KEY}
  claude:
    url: https://api.anthropic.com/v1/messages
    model-key: claude-3-5-sonnet-latest
    model-name: Claude 3 Sonnet
    key: ${CHATBOT_CLAUDE_KEY}

mailchimp:
  api-url: https://mandrillapp.com/api/1.0/messages/send-template.json
  health-url: https://mandrillapp.com/api/1.0/users/info.json
  api-key: ${MAIL_API_KEY}
  from-email: <EMAIL>
  from-name: Digitálny radca
  template-name: digiradca_general

notification:
  begin-testing:
    threshold-days: 10
  finish-testing:
    threshold-days: 10
  couching-options:
    threshold-days: 10

schedule:
  delete-declined-clients: "0 0 3 * * 1" # kazdy pondelok o 3:00
  notification-sender: "0 0 6 * * *" # kazdy den o 6:00

google:
  recaptcha:
    project-id: digiradca-1727447582584
    api-key: ${RECAPTCHA_SECRET_KEY}
    site-key: ${RECAPTCHA_SITE_KEY}
    verify-url: https://recaptchaenterprise.googleapis.com/v1/projects/digiradca-1727447582584/assessments
    enabled: ${RECAPTCHA_ENABLED:true}
    score-threshold: 0.5
    action-mappings:
      - method: GET
        path: /api/chatbot/claude
        action: chatbot_ai_claude
      - method: GET
        path: /api/chatbot/chatgpt
        action: chatbot_ai_chatgpt
      - method: POST
        path: /api/admin/email/not-verified
        action: send_emails_for_unverified
      - method: POST
        path: /api/auth/register
        action: registration
      - method: POST
        path: /api/partner/subject/invite
        action: invite_users
      - method: PATCH
        path: /api/user
        action: update_user
