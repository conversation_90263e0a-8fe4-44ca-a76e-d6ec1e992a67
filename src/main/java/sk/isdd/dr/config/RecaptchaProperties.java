package sk.isdd.dr.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Component
@ConfigurationProperties(prefix = "google.recaptcha")
@Getter
@Setter
public class RecaptchaProperties {

    private String projectId;
    private String apiKey;
    private String siteKey;
    private String verifyUrl;
    private boolean enabled = true;
    private double scoreThreshold = 0.5;
    private List<ActionMapping> actionMappings;

    @Getter
    @Setter
    public static class ActionMapping {
        private String method;
        private String path;
        private String action;
    }

    public String getActionForRequest(String method, String path) {
        if (actionMappings == null) {
            return "default";
        }

        return actionMappings.stream()
                .filter(mapping -> method.equalsIgnoreCase(mapping.getMethod()) && path.equals(mapping.getPath()))
                .map(ActionMapping::getAction)
                .findFirst()
                .orElse("default");
    }

}
