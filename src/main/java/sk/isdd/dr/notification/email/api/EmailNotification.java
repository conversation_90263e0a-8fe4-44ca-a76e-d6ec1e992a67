package sk.isdd.dr.notification.email.api;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import sk.isdd.dr.api.service.api.LogService;
import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.email.EmailTemplateVariables;
import sk.isdd.dr.email.MailchimpSenderService;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.notification.Notification;

public abstract class EmailNotification implements Notification {

    @Autowired
    private MailchimpSenderService mailSender;

    @Autowired
    private LogService logService;

    protected abstract List<SubjectEntity> getSubjects();

    protected abstract EmailTemplateCode getTemplateCode();

    @Override
    public void send() {
        List<SubjectEntity> subjects = getSubjects();
        if (subjects == null || subjects.isEmpty()) {
            return;
        }
        subjects.forEach(subject -> {
            if (!isScheduled() || subject.getLogs().stream().noneMatch(log -> log.getAction() == getAuditLogCode())) {
                mailSender.sendTemplateEmail(subject.getEmail(), getTemplateCode(),
                        new EmailTemplateVariables(subject.getName(), subject.getSurname(), null, subject.getEmail()),
                        null);
                logService.log(null, subject, getAuditLogCode());
            }
        });
    }

}
