package sk.isdd.dr.api.service;

import java.time.LocalDateTime;
import java.util.ArrayList;

import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import sk.isdd.dr.api.dto.Consents;
import sk.isdd.dr.api.dto.Partner;
import sk.isdd.dr.api.dto.Subject;
import sk.isdd.dr.api.dto.request.InvitePartnerClientsRequest;
import sk.isdd.dr.api.dto.response.InvitePartnerClientsResponse;
import sk.isdd.dr.api.exception.business.BadRequestException;
import sk.isdd.dr.api.exception.business.NotFoundException;
import sk.isdd.dr.api.mapper.MainMapper;
import sk.isdd.dr.api.service.api.PartnerService;
import sk.isdd.dr.api.service.api.SubjectService;
import sk.isdd.dr.auth.UserManager;
import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.email.EmailTemplateVariables;
import sk.isdd.dr.email.MailchimpSenderService;
import sk.isdd.dr.jpa.entity.PartnerEntity;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.repository.PartnerRepository;

@Service
public class PartnerServiceImpl implements PartnerService {

    private final UserManager userManager;

    private final MailchimpSenderService mailSender;

    @Autowired
    private SubjectService subjectService;

    private final PartnerRepository partnerRepository;

    private MainMapper mapper;

    public PartnerServiceImpl(UserManager userManager, MailchimpSenderService mailSender,
            PartnerRepository partnerRepository, MainMapper mapper) {
        this.userManager = userManager;
        this.mailSender = mailSender;
        this.partnerRepository = partnerRepository;
        this.mapper = mapper;
    }

    @Override
    public Partner getPartner(Integer partnerId) {
        return mapper.mapPartnerEntity(loadPartner(partnerId));
    }

    @Override
    public Partner updatePartner(Integer partnerId, Partner partner) {
        PartnerEntity partnerEntity = loadPartner(partnerId);
        partnerEntity.setClaim(partner.getClaim());
        partnerEntity.setLogo(partner.getLogo());
        partnerEntity.setModifiedAt(LocalDateTime.now());

        partnerRepository.save(partnerEntity);

        return mapper.mapPartnerEntity(partnerEntity);
    }

    @Override
    @Transactional
    public InvitePartnerClientsResponse inviteClients(Integer partnerId, InvitePartnerClientsRequest request) {
        loadPartner(partnerId);

        InvitePartnerClientsResponse response = new InvitePartnerClientsResponse();
        response.setInvalidEmails(new ArrayList<>());
        response.setSuccessEmails(new ArrayList<>());

        request.getEmails().forEach(email -> {

            try {
                SubjectEntity subjectEntity = subjectService.loadSubjectByEmail(email);
                if (subjectEntity.getPartner() == null || subjectEntity.getPartner().getId() != partnerId) {
                    // subject is created already and does not have given partner ID -> invalid email
                    throw new BadRequestException();
                }
                if (subjectEntity.getPartner() != null && subjectEntity.getPartner().getId() == partnerId) {
                    // subject is created already and has same partner id -> resend invite and update password email
                    throw new NotFoundException();
                }
            } catch (NotFoundException e) {
                // subject is NOT created, create new
                UserRepresentation user = userManager.createClient(email, partnerId, true);
                mailSender.sendTemplateEmail(email, EmailTemplateCode.PARTNER_INVITATION,
                        new EmailTemplateVariables(null, null, request.getInviteText(), email), null);

                Subject subject = new Subject();
                subject.setEmail(user.getEmail());
                subject.setName(null);
                subject.setSurname(null);
                subject.setUserId(user.getId());

                Partner partner = new Partner();
                partner.setId(partnerId);
                subject.setPartner(partner);

                Consents consents = new Consents();
                consents.setMarketing(false);
                consents.setProcessingOfPersonalData(true);
                subject.setConsents(consents);

                subjectService.createSubject(subject);

                response.getSuccessEmails().add(email);
            } catch (BadRequestException e) {
                // subject is created already and is not pre-registered or has password -> invalid email
                response.getInvalidEmails().add(email);
                return;
            }

        });

        return response;
    }

    private PartnerEntity loadPartner(Integer partnerId) {
        return partnerRepository.findById(partnerId).orElseThrow(() -> new NotFoundException("Partner not found"));
    }

}
