package sk.isdd.dr.jpa.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import sk.isdd.dr.jpa.entity.SubjectEntity;

public interface SubjectRepository extends JpaRepository<SubjectEntity, Integer> {

    Optional<SubjectEntity> findByEmail(String email);

    Optional<SubjectEntity> findByUserId(String userId);

    @Query("SELECT s FROM SubjectEntity s WHERE s.deletedAt IS NULL")
    List<SubjectEntity> getAllNotDeleted();

    @Query("SELECT s FROM SubjectEntity s LEFT JOIN s.test t WHERE ((s.verified = false AND s.verifiedAt IS NOT NULL) OR t.id IS NULL) AND s.createdAt < :thresholdDate AND s.deletedAt IS NULL")
    List<SubjectEntity> getDeclinedSubjects(@Param("thresholdDate") LocalDateTime thresholdDate);

    @Query("SELECT s FROM SubjectEntity s JOIN s.attachments a WHERE a.type = 'SIMPLE' AND s.coachRequestedAt IS NULL AND a.createdAt <= :thresholdDate AND s.deletedAt IS NULL")
    List<SubjectEntity> getNotRequestedCoachSubjects(@Param("thresholdDate") LocalDateTime thresholdDate);

    @Query("SELECT s FROM SubjectEntity s LEFT JOIN s.test t WHERE t.id IS NULL AND s.createdAt <= :thresholdDate AND s.deletedAt IS NULL")
    List<SubjectEntity> getNotTestedSubjects(@Param("thresholdDate") LocalDateTime thresholdDate);

    @Query("SELECT s FROM SubjectEntity s WHERE s.idNumber IS NULL AND s.verifiedAt IS NULL AND s.deletedAt IS NULL")
    List<SubjectEntity> getNotVerifiedSubjects();

}
