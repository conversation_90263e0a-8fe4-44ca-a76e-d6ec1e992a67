package sk.isdd.dr.api.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import sk.isdd.dr.api.exception.ErrorResponseWriter;
import sk.isdd.dr.api.exception.business.RecaptchaValidationException;
import sk.isdd.dr.api.service.RecaptchaService;
import sk.isdd.dr.config.RecaptchaProperties;

@Component
public class RecaptchaInterceptor implements HandlerInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecaptchaInterceptor.class);
    private static final String RECAPTCHA_TOKEN_HEADER = "X-Recaptcha-Token";

    private final RecaptchaService recaptchaService;
    private final RecaptchaProperties recaptchaProperties;
    private final ErrorResponseWriter errorResponseWriter;

    public RecaptchaInterceptor(RecaptchaService recaptchaService, RecaptchaProperties recaptchaProperties,
                               ErrorResponseWriter errorResponseWriter) {
        this.recaptchaService = recaptchaService;
        this.recaptchaProperties = recaptchaProperties;
        this.errorResponseWriter = errorResponseWriter;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!recaptchaProperties.isEnabled()) {
            return true;
        }

        String token = request.getHeader(RECAPTCHA_TOKEN_HEADER);
        String method = request.getMethod();
        String path = request.getRequestURI();

        String action = recaptchaProperties.getActionForRequest(method, path);

        LOGGER.debug("Processing reCAPTCHA validation for request: {} {} with action: {}", method, path, action);

        if (!recaptchaService.validateRecaptcha(token, action)) {
            LOGGER.warn("reCAPTCHA validation failed for request: {} {} with action: {}", method, path, action);

            RecaptchaValidationException exception = new RecaptchaValidationException();
            errorResponseWriter.writeError(response, exception.getStatus(), exception.getMessage());
            return false;
        }

        LOGGER.debug("reCAPTCHA validation successful for request: {} {} with action: {}", method, path, action);
        return true;
    }
}
