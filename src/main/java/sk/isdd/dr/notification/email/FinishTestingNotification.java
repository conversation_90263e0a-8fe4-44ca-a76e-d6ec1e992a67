package sk.isdd.dr.notification.email;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.common.enums.LogAction;
import sk.isdd.dr.jpa.entity.SubjectEntity;
import sk.isdd.dr.jpa.repository.TestRepository;
import sk.isdd.dr.notification.email.api.EmailNotification;

@Component
public class FinishTestingNotification extends EmailNotification {

    @Autowired
    private TestRepository testRepository;

    @Value("${notification.finish-testing.threshold-days}")
    private int thresholdDays;

    @Override
    protected List<SubjectEntity> getSubjects() {
        return testRepository.getUnfinishedTests(LocalDateTime.now().minusDays(thresholdDays)).stream()
                .map(t -> t.getSubject()).toList();
    }

    @Override
    protected EmailTemplateCode getTemplateCode() {
        return EmailTemplateCode.NOTIFICATION_FINISH_TESTING;
    }

    @Override
    public boolean isScheduled() {
        return true;
    }

    @Override
    public LogAction getAuditLogCode() {
        return LogAction.SEND_NOTIFICATION_FINISH_TESTING;
    }

}
