package sk.isdd.dr.email;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import sk.isdd.dr.api.dto.Attachment;
import sk.isdd.dr.api.service.api.AttachmentService;
import sk.isdd.dr.common.enums.EmailTemplateCode;
import sk.isdd.dr.email.MailchimpRequest.EmailAttachment;
import sk.isdd.dr.email.MailchimpRequest.MergeVar;
import sk.isdd.dr.jpa.entity.EmailTemplateEntity;
import sk.isdd.dr.jpa.repository.EmailTemplateRepository;

@Service
public class MailchimpSenderService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private EmailTemplateRepository templateRepository;

    @Autowired
    private AttachmentService attachmentService;

    @Value("${mailchimp.api-url}")
    private String apiUrl;

    @Value("${mailchimp.api-key}")
    private String apiKey;

    @Value("${mailchimp.from-email}")
    private String defaultFromEmail;

    @Value("${mailchimp.from-name}")
    private String defaultFromName;

    @Value("${mailchimp.template-name}")
    private String defaultTemplateName;

    public void sendTemplateEmail(String toEmail, EmailTemplateCode templateCode,
            EmailTemplateVariables templateVariables,
            List<Attachment> attachments) {
        EmailTemplateEntity template = templateRepository.findByCode(templateCode)
                .orElseThrow(() -> new IllegalArgumentException("Template not found: " + templateCode));

        MailchimpRequest request = new MailchimpRequest();
        request.setKey(apiKey);
        request.setTemplateName(defaultTemplateName);

        // Fallback template content
        List<MergeVar> contentList = List.of(
                new MergeVar("CONTENT_TITLE", template.getTitle()),
                new MergeVar("CONTENT_TEXT", template.getTemplate()));

        request.setTemplateContent(contentList);

        MailchimpRequest.Message message = new MailchimpRequest.Message();
        message.setFromEmail(defaultFromEmail);
        message.setFromName(defaultFromName);
        message.setTo(List.of(new MailchimpRequest.Recipient(toEmail, "to")));
        message.setSubject(template.getSubject());

        if (templateVariables != null) {
            List<MailchimpRequest.MergeVar> vars = templateVariables.toMap().entrySet().stream()
                    .map(e -> new MailchimpRequest.MergeVar(e.getKey(), e.getValue()))
                    .toList();
            message.setGlobalMergeVars(vars);
        }

        if (attachments != null && !attachments.isEmpty()) {
            message.setAttachments(new ArrayList<>());
            attachments.forEach(attachment -> {
                message.getAttachments().add(new EmailAttachment(
                        attachment.getMimeType(),
                        attachment.getFileName(),
                        convertFileToBase64(
                                attachmentService.getAttachmentFile(attachment.getSubjectId(), attachment.getId()))));
            });
        }

        request.setMessage(message);

        restTemplate.postForEntity(apiUrl, request, String.class);
    }

    private String convertFileToBase64(File file) {
        try {
            byte[] fileContent = Files.readAllBytes(file.toPath());
            return Base64.getEncoder().encodeToString(fileContent);
        } catch (IOException e) {
            throw new RuntimeException("Error reading file: " + file.getAbsolutePath(), e);
        }
    }
}