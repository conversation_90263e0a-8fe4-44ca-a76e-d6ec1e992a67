package sk.isdd.dr.common.enums;

public enum LogAction {

    REGISTER,
    ATTACHMENT_ADDED,
    ATTACHMENT_DELETED,
    COACH_REQUESTED,
    COACH_ASSIGNED,
    USER_PASSWORD_CHANGE,
    <PERSON>ER_UPDATED,
    <PERSON><PERSON>_DELETED,
    SUB<PERSON>ECT_UPDATED,
    SUBJECT_DELETED,
    SUBJECT_REPORTS_APPROVED,
    R<PERSON><PERSON><PERSON><PERSON>ION_APPROVE,
    <PERSON><PERSON><PERSON><PERSON>AT<PERSON>_DECLINE,
    EMAIL_SENT,
    SHL_SCORE_IMPORTED,
    SHL_REPORT_IMPORTED,
    SHL_TEST_COMPLETE,
    SHL_TEST_NOT_COMPLETE,
    SHL_CABINNET_REQUESTED,
    SHL_REGISTRATION_COMPLETED,
    SHL_REGISTRATION_FAILED,
    CABINNET_COMPLETE,
    SEND_NOTIFICATION_BEGIN_TESTING,
    SEND_NOTIFICATION_FINISH_TESTING,
    SEND_NOTIFICATION_COUCHING_OPTIONS,
    SEND_NOTIFICATION_VERIFY,

    // not used, delete in future

    SHL_JOB_MATCHING_IMPORTED,
    ACCOUNT_EMAIL_SENT,
    ADMIN_LOGIN,
    EMAIL_ZOZ_NOT_VERIFIED,
    SHL_JOB_MEASUREMENT_IMPORTED,
    REGISTER_COMPLETE,
    ACCOUNT_GENERATED,
    LOGIN,
    ADMIN_CREATE_USER,
    REGISTRATION_DELETE,
    REGISTER_EMAIL_SENT,
    ZOZ_VERIFICATION,
    USER_PASSWORD_RESET

}
