package sk.isdd.dr.common.validation.validator;

import org.springframework.util.StringUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import sk.isdd.dr.common.util.BirthNumber;

public class BirthNumberValidator implements ConstraintValidator<sk.isdd.dr.common.validation.annotation.BirthNumber, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (!StringUtils.hasText(value)) {
            return false;
        }

        BirthNumber birthNumber = new BirthNumber(value);
        return birthNumber.isValid();
    }
}
