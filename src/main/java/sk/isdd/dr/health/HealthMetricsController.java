package sk.isdd.dr.health;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/health")
public class HealthMetricsController {

    private final HealthCheckService healthCheckService;

    public HealthMetricsController(HealthCheckService healthCheckService) {
        this.healthCheckService = healthCheckService;
    }

    @GetMapping(produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> prometheusHealth() {
        boolean appUp = healthCheckService.isAppUp();
        boolean dbUp = healthCheckService.isDbUp();
        boolean keycloakUp = healthCheckService.isKeycloakUp();
        boolean shlUp = healthCheckService.isShlUp();
        boolean mailchimpUp = healthCheckService.isMailchimpUp();

        StringBuilder sb = new StringBuilder();
        // Expose as Prometheus gauges (1 = up, 0 = down)
        sb.append("# HELP app_up Application availability\n");
        sb.append("# TYPE app_up gauge\n");
        sb.append(String.format("app_up %d\n", appUp ? 1 : 0));

        sb.append("# HELP db_up Database availability\n");
        sb.append("# TYPE db_up gauge\n");
        sb.append(String.format("db_up %d\n", dbUp ? 1 : 0));

        sb.append("# HELP keycloak_up Keycloak availability\n");
        sb.append("# TYPE keycloak_up gauge\n");
        sb.append(String.format("keycloak_up %d\n", keycloakUp ? 1 : 0));

        sb.append("# HELP shl_up SHL SOAP availability\n");
        sb.append("# TYPE shl_up gauge\n");
        sb.append(String.format("shl_up %d\n", shlUp ? 1 : 0));

        sb.append("# HELP mailchimp_up Mailchimp API availability\n");
        sb.append("# TYPE mailchimp_up gauge\n");
        sb.append(String.format("mailchimp_up %d\n", mailchimpUp ? 1 : 0));

        return ResponseEntity.ok(sb.toString());
    }
}
