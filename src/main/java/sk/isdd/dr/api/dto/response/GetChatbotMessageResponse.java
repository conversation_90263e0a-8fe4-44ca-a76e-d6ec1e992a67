package sk.isdd.dr.api.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class GetChatbotMessageResponse {

    @JsonProperty(value = "answer", required = true)
    private final String answer;
    
    @JsonProperty(value = "model-key", required = true)
    private final String modelKey;

    @JsonProperty(value = "model-name", required = true)
    private final String modelName;

}
