package sk.isdd.dr.config;

import java.util.List;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.xml.Jaxb2RootElementHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import sk.isdd.dr.api.interceptor.RecaptchaInterceptor;

@Configuration
public class XmlConfig implements WebMvcConfigurer {

    private final RecaptchaInterceptor recaptchaInterceptor;

    public XmlConfig(RecaptchaInterceptor recaptchaInterceptor) {
        this.recaptchaInterceptor = recaptchaInterceptor;
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.removeIf(c -> c instanceof Jaxb2RootElementHttpMessageConverter);
        converters.add(0, new Jaxb2RootElementHttpMessageConverter());
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(recaptchaInterceptor)
                .addPathPatterns(
                    "/auth/register/**",
                    "/chatbot/**",
                    "/partner/subject/invite",
                    // "/admin/export",
                    "/admin/email/not-verified",
                    "/user"
                    // "/subject/attachment/{attachmentId}",
                    // "/coach/report/subject/{subjectId}"
                    )
                .excludePathPatterns(
                    "/user/password",
                    "/shl/**",           // Exclude all SHL controller endpoints
                    "/swagger-ui/**",    // Exclude Swagger UI
                    "/v3/api-docs/**"    // Exclude API docs
                );
    }

}