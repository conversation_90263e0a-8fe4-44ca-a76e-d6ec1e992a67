package sk.isdd.dr.api.service;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import sk.isdd.dr.config.RecaptchaProperties;

@Service
public class RecaptchaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecaptchaService.class);

    private final RecaptchaProperties recaptchaProperties;
    private final RestTemplate restTemplate;

    public RecaptchaService(RecaptchaProperties recaptchaProperties, RestTemplate restTemplate) {
        this.recaptchaProperties = recaptchaProperties;
        this.restTemplate = restTemplate;
    }

    public boolean validateRecaptcha(String token, String action) {
        if (!recaptchaProperties.isEnabled()) {
            LOGGER.debug("reCAPTCHA Enterprise validation is disabled");
            return true;
        }

        if (token == null || token.trim().isEmpty()) {
            LOGGER.warn("reCAPTCHA token is null or empty");
            return false;
        }

        try {
            String url = String.format("%s?key=%s",
                    recaptchaProperties.getVerifyUrl(),
                    recaptchaProperties.getApiKey());

            Map<String, Object> requestBody = Map.of(
                    "event", Map.of(
                            "token", token,
                            "siteKey", recaptchaProperties.getSiteKey(),
                            "expectedAction", action
                    )
            );

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, request, Map.class);

            if (response.getBody() == null) {
                LOGGER.warn("Empty response from reCAPTCHA Enterprise API");
                return false;
            }

            Map<String, Object> tokenProps = (Map<String, Object>) response.getBody().get("tokenProperties");
            Map<String, Object> riskAnalysis = (Map<String, Object>) response.getBody().get("riskAnalysis");

            boolean valid = tokenProps != null && Boolean.TRUE.equals(tokenProps.get("valid"));
            double score = riskAnalysis != null && riskAnalysis.get("score") != null
                    ? ((Number) riskAnalysis.get("score")).doubleValue()
                    : 0.0;

            LOGGER.info("reCAPTCHA Enterprise validation: valid={}, score={}", valid, score);

            return valid && score >= recaptchaProperties.getScoreThreshold();

        } catch (Exception e) {
            LOGGER.error("Unexpected error validating reCAPTCHA Enterprise token", e);
            return false;
        }
    }
}
